<template>
  <view class="bkcolor" style="background-color: #f5f6f8">
    <!-- 订单 -->
    <view class="wd">
      <view class="my-order-card" v-for="(data, index) in orderList" :key="index">
        <!-- 订单名 -->
        <view style="display: flex; justify-content: space-between">
          <view>
            {{
              dict[data.equityType] === undefined ? "" : dict[data.equityType]
            }}
          </view>
          <view style="color: red">
            {{ getStatus(data) }}
          </view>
        </view>
        <!-- 订单号 -->
        <view
          style="color: #7f7f7f; margin-top: 10px; display: flex; align-items: center; justify-content: space-between;">
          <text>订单号: {{ data.orderno }}</text>
          <view class="copy-btn" @click="copy(data.orderno)">
            <uni-icons type="copy" size="12" color="#00d4aa"></uni-icons>
            <text style="margin-left: 4px; font-size: 12px; color: #00d4aa;">复制</text>
          </view>
        </view>
        <view style="margin-top: 10px"> 手机号:{{ data.phone }} </view>
        <view style="margin-top: 10px"> 归属:{{ data.relationship }} </view>
        <view style="margin-top: 10px">
          {{ formatDate(data.orderTime) }}
        </view>
        <view style="
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
          ">
          <view> 实付:￥{{ data.orderMoney }} </view>
          <view style="
              background-color: #00c66f;
              color: white;
              padding: 5px 15px;
              border-radius: 5px;
            " v-if="type === '0' && data.refundStatus !== 'WAIT_REFUND' && refundButtonVisible" @click="refund(data)">
            申请退款
          </view>
        </view>
      </view>
    </view>
    <view v-if="orderList.length === 0 && isSend === false" style="
        color: #7f7f7f;
        position: fixed;
        top: 30%;
        left: 50%;
        transform: translateX(-50%);
      ">
      <view style="text-align: center"> 暂无订单 </view>
    </view>
    <view v-if="isSend === true" style="
        color: #7f7f7f;
        position: fixed;
        top: 30%;
        left: 50%;
        transform: translateX(-50%);
      ">
      加载中
    </view>
  </view>
</template>

<script>
import {
  getOrdersDetailsInfoByStatus,
  requestARefund,
  allcommodity,
} from "@/subpackages/proxy/api/proxyOrderList.js";
import { getUserId, isLogin, isProxyLogin } from "@/utils/auth.js";
import { getMenuConfig } from "@/api/menu.js";
// const dict = ['', '黄金卡', '钻石卡']
export default {
  data() {
    return {
      range: [],
      type: "",
      orderList: [],
      dict: {},
      isSend: false,
      refundButtonVisible: true, // 控制申请退款按钮的显示/隐藏
    };
  },
  methods: {
    getOrdersDetailsInfoByStatus() {
      const data = {
        userid: getUserId(),
        startTime: this.range[0] + " 00:00:00",
        endTime: this.range[1] + " 23:59:59",
        orderType: this.type,
      };
      this.isSend = true;
      getOrdersDetailsInfoByStatus(data)
        .then((res) => {
          console.log(res);
          res = res.data;
          if (res.code !== 200) {
            return;
          }
          if (this.type === "0") {
            this.orderList = res.data.allCompleteOrders;
          } else {
            this.orderList = res.data.allRefundOrders;
          }
          console.log(this.orderList);
        })
        .finally(() => {
          this.isSend = false;
        });
    },
    getStatus(obj) {
      if (!obj.refundStatus) {
        if (obj.orderStatus === "COMPLETE") {
          return "已完成";
        } else if (obj.orderStatus === "FAILED") {
          return "已失败";
        }
        return "待支付";
      }
      if (obj.refundStatus === "WAIT_REFUND") {
        return "待退款";
      } else if (obj.refundStatus === "REFUND") {
        return "已退款";
      } else if (obj.refundStatus === "REFUNDING") {
        return "退款中";
      }
      return "退款失败";
    },
    formatDate(str = "") {
      const data1 = str.split(" ");
      const data2 = data1[0].split("/");
      return data2[0] + "年" + data2[1] + "月" + data2[2] + "日" + data1[1];
    },
    refund(order) {
      if (order.refundStatus === "WAIT_REFUND") {
        uni.showToast({
          duration: 2000,
          icon: "error",
          title: "订单已申请退款",
        });
        return;
      }

      uni.showModal({
        title: "退款",
        content: "是否对该订单申请退款",
        success: (res) => {
          if (res.cancel) {
            return;
          }
          requestARefund({ orderno: order.orderno }).then((res) => {
            console.log(res);
            res = res.data;
            if (res.code !== 200) {
              uni.showToast({
                icon: "error",
                title: "申请失败",
                duration: 1500,
              });
              return;
            }
            uni.showToast({
              icon: "success",
              title: "申请成功",
              duration: 1500,
            });
            order.refundStatus = "WAIT_REFUND";
          });
        },
      });
    },
    copy(text) {
      // 复制功能
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: "复制成功",
            icon: "success",
          });
        },
        fail: () => {
          uni.showToast({
            title: "复制失败",
            icon: "none",
          });
        },
      });
    },

    allcommodity() {
      allcommodity().then((res) => {
        res = res.data;
        if (res.code !== 200) {
          return;
        }
        res.data.forEach((o) => {
          this.dict[o.id] = o.name;
        });
        this.dict = { ...this.dict };
      });
    },

    // 获取菜单配置并检查申请退款按钮的显示状态
    async checkRefundButtonVisibility() {
      try {
        const res = await getMenuConfig();

        if (res.data && res.data.code === 200) {
          const menuConfig = res.data.data || [];

          // 查找 cornName 为 "申请退款" 的配置项
          const refundConfig = menuConfig.find(config => config.cornName === "申请退款");

          if (refundConfig) {
            // status为1时显示，status为0时隐藏
            this.refundButtonVisible = refundConfig.status === 1;
            console.log("申请退款按钮配置:", refundConfig, "显示状态:", this.refundButtonVisible);
          } else {
            // 没有找到配置，默认显示
            this.refundButtonVisible = true;
            console.log("未找到申请退款配置，默认显示");
          }
        } else {
          // 接口调用失败，默认显示
          this.refundButtonVisible = true;
          console.warn("获取菜单配置失败，默认显示申请退款按钮");
        }
      } catch (error) {
        console.error("获取菜单配置出错:", error);
        // 出错时默认显示
        this.refundButtonVisible = true;
      }
    },
  },
  async onLoad(query) {
    // 检查代理端登录状态
    if (!isLogin() || !isProxyLogin()) {
      console.log("用户未登录或非代理端登录，跳转到统一登录页");
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
      });
      return;
    }
    const data = JSON.parse(query.data);
    this.range = data.range;
    this.type = data.type;

    // 检查申请退款按钮的显示状态
    await this.checkRefundButtonVisibility();

    this.getOrdersDetailsInfoByStatus();
    this.allcommodity();
  },
};
</script>

<style lang="scss">
/* 页面容器 */
.bkcolor {
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
  min-height: 100vh;
}

/* 内容区域 */
.wd {
  padding: 20px;
}

/* 订单卡片 */
.my-order-card {
  background: white;
  border-radius: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
  transition: all 0.3s ease;
  padding: 0;

  &:active {
    transform: scale(0.98);
  }

  /* 订单头部区域 */
  >view:first-child {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 16px;
    border-bottom: 1px solid #f5f5f5;

    >view:first-child {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      line-height: 1.4;
    }

    >view:last-child {
      padding: 6px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
      letter-spacing: 0.3px;
      background: rgba(76, 201, 100, 0.1);
      color: #4cd964;
      border: 1px solid rgba(76, 201, 100, 0.2);
    }
  }

  /* 订单信息区域 */
  >view:not(:first-child) {
    padding: 0 20px;
    margin-top: 0;

    &:last-child {
      padding-bottom: 20px;
    }
  }

  /* 订单号行 */
  >view:nth-child(2) {
    padding-top: 16px;
    color: #666;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0;

    .copy-btn {
      display: flex;
      align-items: center;
      padding: 4px 8px;
      background: rgba(0, 212, 170, 0.1);
      border-radius: 8px;
      transition: all 0.2s ease;

      &:active {
        background: rgba(0, 212, 170, 0.2);
      }

      text {
        margin-left: 4px;
        font-size: 12px;
        color: #00d4aa;
        font-weight: 500;
      }
    }
  }

  /* 手机号、归属、时间行 */
  >view:nth-child(3),
  >view:nth-child(4),
  >view:nth-child(5) {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-top: 12px;
    padding-top: 0;
    padding-bottom: 0;
  }

  /* 底部实付金额和按钮行 */
  >view:last-child {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 16px;
    padding-bottom: 20px;
    border-top: 1px solid #f5f5f5;

    >view:first-child {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    >view:last-child {
      background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 600;
      box-shadow: 0 2px 8px rgba(0, 212, 170, 0.3);
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.95);
        box-shadow: 0 1px 4px rgba(0, 212, 170, 0.4);
      }
    }
  }
}

/* 空状态样式 */
view[style*="position: fixed"][style*="top: 30%"] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 16px;

  >view {
    text-align: center;
    margin-bottom: 8px;
  }
}

/* 加载状态样式 */
view[style*="position: fixed"][style*="top: 30%"]:last-child {
  color: #00d4aa;
  font-size: 16px;
  font-weight: 500;
}
</style>
