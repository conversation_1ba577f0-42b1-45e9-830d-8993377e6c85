<template>
  <view class="settings-container">
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 用户信息卡片 -->
      <view class="user-card">
        <view class="card-title">基本信息</view>
        <!-- 头像和手机号区域 -->
        <view class="avatar-section">
          <view class="avatar-wrapper" @click="chooseImage">
            <image v-if="userInfo.headPic === ''" :src="`${config.BASE_URL}/static/photo.png`" class="user-avatar">
            </image>
            <image v-else :src="userInfo.headPic" class="user-avatar"></image>
            <view class="avatar-badge">
              <uni-icons type="camera" size="16" color="#fff" class="camera-icon"></uni-icons>
            </view>
          </view>
          <view class="user-info">
            <view class="info-label">手机号</view>
            <view class="phone-number">{{
              userInfo.phone || "未绑定手机"
            }}</view>
          </view>
        </view>

        <!-- 用户名编辑 -->
        <view class="info-edit-group">
          <view class="edit-label">用户名</view>
          <view class="input-wrapper">
            <view class="input-icon">
              <uni-icons type="person" size="16" color="#999" class="person-icon"></uni-icons>
            </view>
            <input class="form-input" type="text" placeholder="请输入用户名" v-model="editUserName"
              placeholder-class="input-placeholder" />
            <button class="save-btn" @click="saveUserName" :disabled="isSaveDisabled">
              <text>保存</text>
            </button>
          </view>
        </view>
      </view>

      <!-- 代理商信息卡片 -->
      <view class="info-card">
        <view class="card-title">代理商信息</view>
        <view class="info-grid">
          <view class="info-item">
            <view class="info-icon">
              <uni-icons type="star" size="20" color="#00d4aa" class="star-icon"></uni-icons>
            </view>
            <view class="info-content">
              <view class="info-label">代理等级</view>
              <view class="info-value level">{{
                rankDict[userInfo.adminRabk] || "代理商"
              }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮区域 -->
      <view class="action-section">
        <button class="change-password-btn" @click="showChangePasswordModal">
          <text>修改密码</text>
        </button>
        <button class="logout-btn" @click="logout">
          <text>退出登录</text>
        </button>
      </view>
    </view>

    <!-- 修改密码弹窗 -->
    <view v-if="showPasswordModal" class="modal-overlay" @click="hideChangePasswordModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <view class="modal-title">修改密码</view>
          <view class="modal-close" @click="hideChangePasswordModal">
            <uni-icons type="close" size="20" color="#999"></uni-icons>
          </view>
        </view>
        <view class="modal-body">
          <view class="password-input-group">
            <view class="input-label">新密码</view>
            <view class="password-input-wrapper">
              <input class="password-input" type="password" placeholder="请输入新密码（至少6位）" v-model="newPassword"
                placeholder-class="input-placeholder" />
            </view>
          </view>
          <view class="password-input-group">
            <view class="input-label">确认密码</view>
            <view class="password-input-wrapper">
              <input class="password-input" type="password" placeholder="请再次输入新密码" v-model="confirmPassword"
                placeholder-class="input-placeholder" />
            </view>
          </view>
        </view>
        <view class="modal-footer">
          <button class="modal-btn cancel-btn" @click="hideChangePasswordModal">
            <text>取消</text>
          </button>
          <button class="modal-btn confirm-btn" @click="changePassword" :disabled="isChangingPassword">
            <text>{{ isChangingPassword ? '修改中...' : '确认修改' }}</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  showInformations,
  setUserName,
  uploadImage,
  updatePassword,
} from "@/subpackages/proxy/api/proxyUser.js";
import { validPassword } from "@/utils/valid.js";
import { resetInfo, clearProxyLoginStatus } from "@/utils/auth.js";
import { mapGetters } from "vuex";
import config from "@/utils/config.js";

export default {
  data() {
    return {
      config,
      isSend: false,
      rankDict: ["", "1级代理", "2级代理", "总代理"],
      headPicUrl: "", // 新头像URL
      editUserName: "", // 编辑中的用户名
      // 修改密码相关
      showPasswordModal: false,
      newPassword: "",
      confirmPassword: "",
      isChangingPassword: false,
    };
  },
  computed: {
    ...mapGetters("proxyUser", ["userInfo"]),
    // 计算保存按钮是否应该禁用
    isSaveDisabled() {
      return this.isSend || !this.editUserName || !this.editUserName.trim();
    },
  },
  watch: {
    // 监听userInfo变化，初始化编辑用户名
    userInfo: {
      handler(newVal) {
        if (newVal && newVal.userName) {
          this.editUserName = newVal.userName;
        }
      },
      immediate: true,
    },
    isSend(c) {
      if (c) {
        uni.showLoading({
          title: "加载中",
          mask: true,
        });
      } else {
        uni.hideLoading();
      }
    },
  },
  methods: {
    // 保存用户名
    saveUserName() {
      if (!this.editUserName.trim()) {
        uni.showToast({
          title: "请输入用户名",
          icon: "error",
        });
        return;
      }

      if (this.isSend) {
        return;
      }

      this.isSend = true;
      const data = {
        username: this.editUserName.trim(),
        headPic: this.userInfo.headPic, // 保持头像不变
      };

      setUserName(data)
        .then((res) => {
          if (res && res.data.code === 200) {
            uni.showToast({
              title: "用户名更新成功",
              icon: "success",
            });
            // 更新用户信息
            this.$store.dispatch("proxyUser/getUserInfo");
          } else {
            throw new Error(res.data.message || "用户名更新失败");
          }
        })
        .catch((err) => {
          console.error("用户名更新失败:", err);
          uni.showToast({
            title: err.message || "用户名更新失败",
            icon: "error",
          });
        })
        .finally(() => {
          this.isSend = false;
        });
    },
    logout() {
      uni.showModal({
        title: "提示",
        content: "确定要退出代理端吗？",
        success: (res) => {
          if (res.confirm) {
            // 清除代理端登录状态
            clearProxyLoginStatus();
            this.$store.commit("proxyUser/DEFAULTUSERINFO");

            // 跳转到统一登录页面
            uni.reLaunch({
              url: "/pages/unifiedLogin/unifiedLogin",
            });
          }
        },
      });
    },
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.uploadImageFile(tempFilePath);
        },
      });
    },
    uploadImageFile(filePath) {
      if (this.isSend) {
        return;
      }
      this.isSend = true;
      uploadImage(filePath)
        .then((res) => {
          if (res.data.code === 200) {
            this.headPicUrl = res.data.data;
            // 直接更新用户头像
            const data = {
              username: this.userInfo.userName, // 保持用户名不变
              headPic: res.data.data,
            };
            return setUserName(data);
          } else {
            throw new Error("图片上传失败");
          }
        })
        .then((res) => {
          if (res && res.data.code === 200) {
            uni.showToast({
              title: "头像更新成功",
              icon: "success",
            });
            this.$store.dispatch("proxyUser/getUserInfo");
          } else {
            throw new Error("头像更新失败");
          }
        })
        .catch((err) => {
          console.error("上传失败:", err);
          uni.showToast({
            title: err.message || "头像更新失败",
            icon: "error",
          });
        })
        .finally(() => {
          this.isSend = false;
        });
    },

    // 显示修改密码弹窗
    showChangePasswordModal() {
      this.showPasswordModal = true;
      this.newPassword = "";
      this.confirmPassword = "";
    },

    // 隐藏修改密码弹窗
    hideChangePasswordModal() {
      this.showPasswordModal = false;
      this.newPassword = "";
      this.confirmPassword = "";
      this.isChangingPassword = false;
    },

    // 修改密码
    changePassword() {
      // 验证新密码
      if (!validPassword(this.newPassword)) {
        uni.showToast({
          title: "密码至少6位",
          icon: "error",
        });
        return;
      }

      // 验证确认密码
      if (this.newPassword !== this.confirmPassword) {
        uni.showToast({
          title: "两次密码不一致",
          icon: "error",
        });
        return;
      }

      if (this.isChangingPassword) {
        return;
      }

      this.isChangingPassword = true;

      const data = {
        id: this.userInfo.id,
        password: this.newPassword,
      };

      updatePassword(data)
        .then((res) => {
          if (res && res.data.code === 200) {
            uni.showToast({
              title: "密码修改成功，即将退出登录",
              icon: "success",
            });
            this.hideChangePasswordModal();

            // 延迟1.5秒后自动退出登录
            setTimeout(() => {
              this.autoLogout();
            }, 1500);
          } else {
            throw new Error(res.data.message || "密码修改失败");
          }
        })
        .catch((err) => {
          console.error("密码修改失败:", err);
          uni.showToast({
            title: err.message || "密码修改失败",
            icon: "error",
          });
        })
        .finally(() => {
          this.isChangingPassword = false;
        });
    },

    // 自动退出登录（密码修改成功后）
    autoLogout() {
      // 清除代理端登录状态
      clearProxyLoginStatus();
      this.$store.commit("proxyUser/DEFAULTUSERINFO");

      // 跳转到统一登录页面
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin",
      });
    },
  },
  onHide() {
    uni.hideLoading();
  },
};
</script>

<style lang="scss" scoped>
.settings-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 主要内容 */
.main-content {
  padding: 20px;
}

/* 用户信息卡片 */
.user-card {
  background: #fff;
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.08);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  letter-spacing: 0.3px;
}

/* 头像区域 */
.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.avatar-wrapper {
  position: relative;
  margin-right: 20px;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.user-avatar {
  width: 70px;
  height: 70px;
  border-radius: 20px;
  border: 3px solid #00d4aa;
  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.2);
}

.avatar-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  background: #00d4aa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #fff;
}

.user-info {
  flex: 1;
}

.info-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.phone-number {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  letter-spacing: 0.3px;
}

/* 信息编辑组 */
.info-edit-group {
  margin-bottom: 20px;
}

.edit-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  min-height: 44px;
  padding: 0 12px 0 40px;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.form-input {
  flex: 1;
  font-size: 15px;
  color: #333;
  font-weight: 500;
  border: none;
  background: transparent;
  outline: none;
  padding: 0;
  margin-right: 10px;
}

.input-placeholder {
  color: #999;
  font-size: 15px;
}

.save-btn {
  background: #00d4aa;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  min-width: 50px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:disabled {
    background: #ccc;
    color: #999;
  }

  &:not(:disabled):active {
    transform: scale(0.95);
    background: #00b894;
  }

  text {
    color: inherit;
    font-size: 12px;
    font-weight: 500;
  }
}

/* 信息卡片 */
.info-card {
  background: #fff;
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.08);
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.info-icon {
  width: 40px;
  height: 40px;
  background: rgba(0, 212, 170, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.info-content {
  flex: 1;
}

.info-value {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  letter-spacing: 0.3px;

  &.level {
    color: #00d4aa;
    font-size: 18px;
  }
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
}

.action-section button {
  width: 100%;
  height: 50px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-sizing: border-box;
  letter-spacing: 1px;

  &:active {
    transform: scale(0.98);
  }

  text {
    color: inherit;
    font-size: 16px;
    font-weight: 500;
  }
}

.change-password-btn {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;

  &:active {
    background: linear-gradient(135deg, #00b894 0%, #008f7a 100%);
  }
}

.logout-btn {
  background: #fff;
  color: #ff6b6b;
  border: 2px solid #ff6b6b;
  display: flex;
  justify-content: center;
  align-items: center;

  &:active {
    background: #ff6b6b;
    color: #fff;
  }
}

/* Custom icon styles */
.camera-icon {
  color: #fff;
  font-size: 12px;
}

.person-icon {
  color: #999 !important;
  font-size: 16px !important;
}

.star-icon {
  color: #00d4aa;
  font-size: 20px;
}

/* 修改密码弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  box-sizing: border-box;
}

.modal-content {
  background: #fff;
  border-radius: 20px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 25px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;

  &:active {
    background: #f5f5f5;
  }
}

.modal-body {
  padding: 25px;
}

.password-input-group {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.input-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.password-input-wrapper {
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  padding: 12px 15px;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #00d4aa;
    background: #fff;
  }
}

.password-input {
  width: 100%;
  font-size: 15px;
  color: #333;
  border: none;
  background: transparent;
  outline: none;
  font-weight: 500;
}

.modal-footer {
  display: flex;
  gap: 15px;
  padding: 20px 25px;
  border-top: 1px solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 44px;
  border: none;
  border-radius: 22px;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    transform: scale(0.98);
  }

  text {
    color: inherit;
    font-size: 15px;
    font-weight: 500;
  }
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;

  &:active {
    background: #e8e8e8;
  }
}

.confirm-btn {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  color: #fff;

  &:disabled {
    background: #ccc;
    color: #999;
  }

  &:not(:disabled):active {
    background: linear-gradient(135deg, #00b894 0%, #008f7a 100%);
  }
}
</style>
