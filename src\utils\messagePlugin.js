import { Message } from "element-ui";
import { handleError } from "./errorHandler";

/**
 * Vue消息插件，扩展$message方法支持自动中文化
 */
export default {
  install(Vue) {
    // 保存原始的$message方法
    const originalMessage = Message;

    // 创建一个包装函数来处理直接调用 $message() 的情况
    const messageWrapper = function(options) {
      if (typeof options === 'string') {
        return originalMessage.info(options);
      }
      return originalMessage(options);
    };

    // 扩展$message方法
    Vue.prototype.$message = Object.assign(messageWrapper, {
      error: (message) => {
        // 使用我们的错误处理工具进行中文化
        handleError(message, message, true);
      },
      // 保持其他方法不变
      success: originalMessage.success,
      warning: originalMessage.warning,
      info: originalMessage.info,
      close: originalMessage.close,
      closeAll: originalMessage.closeAll,
    });

    // 提供快捷方法
    Vue.prototype.$showError = (
      error,
      defaultMessage = "操作失败，请稍后重试"
    ) => {
      handleError(error, defaultMessage);
    };
  },
};
