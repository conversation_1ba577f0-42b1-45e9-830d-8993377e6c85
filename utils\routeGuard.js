import {
  isLogin,
  isProxyLogin,
  checkLoginStatus,
  isUserLogin,
  isProxyLoginStrict,
  getUserToken,
  getProxyToken
} from "./auth.js";
import sessionManager from "./sessionManager.js";
import store from "@/store/index.js";

/**
 * 获取当前用户的adminRank
 * @returns {string} adminRank值 ("0"=普通用户, "1","2","3"=代理)
 */
function getCurrentUserAdminRank() {
  try {
    // 先检查登录状态，如果未登录则直接返回默认值
    const userLoggedIn = isUserLogin();
    const proxyLoggedIn = isProxyLoginStrict();

    if (!userLoggedIn && !proxyLoggedIn) {
      console.log("[RouteGuard] 用户未登录，返回默认adminRank: 0");
      return "0";
    }

    // 如果有代理端登录状态，优先从代理端store获取
    if (proxyLoggedIn) {
      const proxyUserInfo = store.getters.proxyUserInfo;
      if (proxyUserInfo && proxyUserInfo.adminRabk) {
        return proxyUserInfo.adminRabk;
      }
    }

    // 如果有用户端登录状态，从用户端store获取
    if (userLoggedIn) {
      const userInfo = store.getters.userInfo;
      if (userInfo && userInfo.adminRabk) {
        return userInfo.adminRabk;
      }
    }

    // 默认返回普通用户
    return "0";
  } catch (error) {
    console.warn("[RouteGuard] 获取adminRank失败:", error);
    return "0";
  }
}

/**
 * 检查用户是否有权限访问指定类型的页面
 * @param {string} pageType - 页面类型 ("user" | "proxy")
 * @returns {boolean} 是否有权限
 */
function checkUserPermissionByAdminRank(pageType) {
  const adminRank = getCurrentUserAdminRank();

  if (pageType === "user") {
    // 用户端页面：只允许adminRank="0"的用户访问
    return adminRank === "0";
  } else if (pageType === "proxy") {
    // 代理端页面：只允许adminRank="1","2","3"的用户访问
    return ["1", "2", "3"].includes(adminRank);
  }

  return false;
}

/**
 * 检查是否需要登录认证的页面
 */
const AUTH_REQUIRED_PAGES = [
  // 用户端需要登录的页面
  // "/pages/my/my", // 移除首页登录要求，允许审核人员查看界面
  "/subpackages/user/pages/setUserInfo/setUserInfo",
  "/subpackages/user/pages/myOrder/myOrder",
  "/pages/buy/buy",
  "/pages/goodInfo/goodInfo",
  "/pages/flOrder/flOrder",
  "/subpackages/user/pages/bindingCarId/bindingCarId",
  "/subpackages/user/pages/withdrawal/withdrawal",
  "/subpackages/shop/pages/selfOrderBuy/selfOrderBuy",
  "/subpackages/shop/pages/selfOrderBuy/addressList",
  "/subpackages/shop/pages/selfOrderBuy/editAddress",

  // 代理端需要登录的页面
  "/subpackages/proxy/pages/proxyHome/proxyHome",
  "/pages/proxyIndex/index",
  "/subpackages/proxy/pages/proxySetUserInfo/setUserInfo",
  "/subpackages/proxy/pages/proxyUserList/userList",
  "/subpackages/proxy/pages/proxyOrderList/orderList",
  "/subpackages/proxy/pages/proxyPerformance/performance",
  "/subpackages/proxy/pages/proxyProxyPerformance/proxyPerformance",
  "/subpackages/proxy/pages/proxyDevelopment/development",
  "/subpackages/proxy/pages/proxyUserProtocol/userProtocol",
];

/**
 * 代理端专属页面
 */
const PROXY_ONLY_PAGES = [
  "/subpackages/proxy/pages/proxyHome/proxyHome",
  "/pages/proxyIndex/index",
  "/subpackages/proxy/pages/proxyLogin/proxyLogin",
  "/pages/proxyRegister/register",
  "/subpackages/proxy/pages/proxySetUserInfo/setUserInfo",
  "/subpackages/proxy/pages/proxyUserList/userList",
  "/subpackages/proxy/pages/proxyOrderList/orderList",
  "/subpackages/proxy/pages/proxyPerformance/performance",
  "/subpackages/proxy/pages/proxyProxyPerformance/proxyPerformance",
  "/subpackages/proxy/pages/proxyDevelopment/development",
  "/subpackages/proxy/pages/proxyUserProtocol/userProtocol",
];

/**
 * 用户端专属页面
 */
const USER_ONLY_PAGES = [
  "/pages/index/index",
  "/subpackages/goods/pages/equities/equities",
  "/subpackages/services/pages/ETCService/ETCService",
  "/subpackages/user/pages/login/login",
  "/subpackages/user/pages/setUserInfo/setUserInfo",
  "/pages/service/service",
  "/subpackages/user/pages/myOrder/myOrder",
  "/pages/buy/buy",
  "/pages/webPage/webPage",
  "/pages/goodInfo/goodInfo",
  "/pages/flOrder/flOrder",
  "/subpackages/user/pages/bindingCarId/bindingCarId",
  "/subpackages/user/pages/withdrawal/withdrawal",
  "/pages/goodsfilter/goodsList",
  "/pages/goodsfilter/goodsDetails",
  "/subpackages/common/pages/userProtocol/userProtocol",
  "/subpackages/common/pages/privacyPolicy/privacyPolicy",
  "/subpackages/common/pages/service/service",
  "/subpackages/shop/pages/selfOrderBuy/selfOrderBuy",
  "/subpackages/shop/pages/selfOrderBuy/addressList",
  "/subpackages/shop/pages/selfOrderBuy/editAddress",
];

/**
 * 公共页面（两端都可以访问）
 */
const PUBLIC_PAGES = [
  "/pages/wecome/wecome",
  "/pages/unifiedLogin/unifiedLogin",
  "/pages/my/my", // 用户端首页，未登录也可以访问
];

/**
 * 路由守卫 - 页面跳转前的权限检查
 * @param {string} url - 要跳转的页面路径
 * @returns {Object} 检查结果 { canAccess: boolean, redirectUrl?: string, message?: string }
 */
export function routeGuard(url) {
  // 移除查询参数，只保留路径
  const path = url.split("?")[0];

  console.log("[RouteGuard] 检查页面访问权限:", path);

  // 检查是否为公共页面
  if (PUBLIC_PAGES.includes(path)) {
    return { canAccess: true };
  }

  // 检查是否需要登录
  const needAuth = AUTH_REQUIRED_PAGES.includes(path);
  const userLoggedIn = isLogin();
  const isProxyUser = isProxyLogin();
  const adminRank = getCurrentUserAdminRank();

  console.log("[RouteGuard] 登录状态:", {
    userLoggedIn,
    isProxyUser,
    needAuth,
    adminRank,
  });

  // 代理端首页特殊处理：允许任何用户预览访问（无论是否登录）
  if (path === "/pages/proxyIndex/index") {
    return { canAccess: true };
  }

  // 需要登录但未登录
  if (needAuth && !userLoggedIn) {
    return {
      canAccess: false,
      redirectUrl: "/pages/unifiedLogin/unifiedLogin",
      message: "请先登录",
    };
  }

  // 严格的权限检查：基于adminRank和登录状态
  if (userLoggedIn) {
    // 代理端用户访问用户端页面
    if (USER_ONLY_PAGES.includes(path)) {
      // 检查是否为普通用户（adminRank="0"）且不是代理端登录
      if (adminRank !== "0" || isProxyUser) {
        return {
          canAccess: false,
          redirectUrl: "/pages/proxyIndex/index",
          message: "代理用户无法访问用户端页面",
        };
      }
    }

    // 用户端用户访问代理端页面（除了首页，首页已经在上面处理了）
    if (PROXY_ONLY_PAGES.includes(path)) {
      // 其他代理端页面需要检查是否为代理用户（adminRank="1","2","3"）且是代理端登录
      if (!["1", "2", "3"].includes(adminRank) || !isProxyUser) {
        return {
          canAccess: false,
          redirectUrl: "/pages/my/my",
          message: "普通用户无法访问代理端页面",
        };
      }
    }

    // 额外检查：确保登录状态与adminRank一致
    if (adminRank === "0" && isProxyUser) {
      // 普通用户但是代理端登录状态，清除代理端状态
      console.warn("[RouteGuard] 检测到状态不一致：普通用户但有代理端登录状态");
      return {
        canAccess: false,
        redirectUrl: "/pages/unifiedLogin/unifiedLogin",
        message: "登录状态异常，请重新登录",
      };
    }

    if (["1", "2", "3"].includes(adminRank) && !isProxyUser) {
      // 代理用户但没有代理端登录状态
      console.warn("[RouteGuard] 检测到状态不一致：代理用户但无代理端登录状态");
      return {
        canAccess: false,
        redirectUrl: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
        message: "登录状态异常，请重新登录",
      };
    }
  }

  return { canAccess: true };
}

/**
 * 安全的页面跳转函数 - 带权限检查
 * @param {Object} options - 跳转参数
 * @param {string} options.url - 目标页面路径
 * @param {string} options.method - 跳转方法 (navigateTo|redirectTo|reLaunch|switchTab)
 * @param {Object} options.params - 额外参数
 */
export function safeNavigate(options) {
  const { url, method = "navigateTo", ...params } = options;

  const guardResult = routeGuard(url);

  if (!guardResult.canAccess) {
    // 显示提示信息
    if (guardResult.message) {
      uni.showToast({
        title: guardResult.message,
        icon: "none",
        duration: 2000,
      });
    }

    // 重定向到正确的页面
    if (guardResult.redirectUrl) {
      setTimeout(() => {
        uni.reLaunch({
          url: guardResult.redirectUrl,
        });
      }, 2000);
    }

    return false;
  }

  // 执行跳转
  const navigationMethods = {
    navigateTo: uni.navigateTo,
    redirectTo: uni.redirectTo,
    reLaunch: uni.reLaunch,
    switchTab: uni.switchTab,
  };

  const navigationMethod = navigationMethods[method];
  if (navigationMethod) {
    navigationMethod({
      url,
      ...params,
    });
    return true;
  } else {
    console.error("[RouteGuard] 无效的导航方法:", method);
    return false;
  }
}

/**
 * 检查当前页面是否有访问权限
 * 用于页面的 onLoad 生命周期中
 * @param {boolean} skipRedirect - 是否跳过自动重定向，只返回检查结果
 */
export function checkCurrentPageAccess(skipRedirect = false) {
  const pages = getCurrentPages();
  if (pages.length === 0) return true;

  const currentPage = pages[pages.length - 1];
  const currentPath = "/" + currentPage.route;

  const guardResult = routeGuard(currentPath);

  if (!guardResult.canAccess) {
    console.warn("[RouteGuard] 当前页面无访问权限:", currentPath);

    if (!skipRedirect) {
      // 显示提示信息
      if (guardResult.message) {
        uni.showToast({
          title: guardResult.message,
          icon: "none",
          duration: 2000,
        });
      }

      // 重定向到正确的页面
      if (guardResult.redirectUrl) {
        setTimeout(() => {
          uni.reLaunch({
            url: guardResult.redirectUrl,
          });
        }, 2000);
      }
    }

    return false;
  }

  return true;
}

/**
 * 获取默认首页路径
 * 根据登录状态和adminRank返回合适的首页
 */
export function getDefaultHomePage() {
  if (!isLogin()) {
    return "/pages/my/my"; // 未登录也可以访问首页
  }

  const adminRank = getCurrentUserAdminRank();
  const isProxyUser = isProxyLogin();

  // 基于adminRank和登录状态决定首页
  if (["1", "2", "3"].includes(adminRank) && isProxyUser) {
    return "/pages/proxyIndex/index";
  } else if (adminRank === "0" && !isProxyUser) {
    return "/pages/my/my";
  } else {
    // 状态不一致，跳转到登录页
    console.warn("[RouteGuard] 登录状态与adminRank不一致，跳转到登录页");
    return "/pages/unifiedLogin/unifiedLogin";
  }
}

/**
 * 登出后的页面重定向
 */
export function redirectAfterLogout() {
  uni.reLaunch({
    url: "/pages/unifiedLogin/unifiedLogin",
  });
}
