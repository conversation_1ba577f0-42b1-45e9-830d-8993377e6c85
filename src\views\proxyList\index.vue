﻿<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-card">
        <div class="search-header">
          <i class="el-icon-search search-icon" />
          <span class="search-title">代理查询</span>
        </div>

        <div class="search-content">
          <div class="search-input-wrapper">
            <el-input v-model="phone" placeholder="请输入手机号" prefix-icon="el-icon-mobile-phone" clearable
              @keyup.enter.native="searchItem" />
          </div>

          <div class="search-select-wrapper">
            <el-select v-model="adminRabk" placeholder="请选择代理级别" prefix-icon="el-icon-user">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>

          <div class="search-button-wrapper">
            <el-button type="primary" icon="el-icon-search" @click="searchItem">
              查询
            </el-button>
            <el-button type="success" icon="el-icon-plus" @click="open">
              新增一级代理
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">
            <i class="el-icon-user-solid" />
            <span>代理列表</span>
          </div>
        </div>

        <el-table :data="userList" stripe style="width: 100%" class="modern-table" :header-cell-style="{
          background: '#f8fafc',
          color: '#374151',
          fontWeight: '600',
        }" :row-style="{ height: '60px' }">
          <el-table-column prop="id" label="用户ID" width="120" align="center">
            <template slot-scope="scope">
              <span class="user-id">#{{ scope.row.id }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="phone" label="手机号" min-width="140">
            <template slot-scope="scope">
              <span class="phone-number">{{ scope.row.phone }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="userName" label="姓名" min-width="220">
            <template slot-scope="scope">
              <div class="user-name-cell">
                <div class="user-avatar">
                  {{ scope.row.userName.charAt(0) }}
                </div>
                <span>{{ scope.row.userName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="integral" label="积分" width="100" align="center">
            <template slot-scope="scope">
              <el-tag type="success" size="small">
                {{ scope.row.integral }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="invitationCode" label="邀请码" min-width="140">
            <template slot-scope="scope">
              <span class="invitation-code">
                {{
                  scope.row.invitationCode !== null
                    ? scope.row.invitationCode
                    : "自主注册"
                }}
              </span>
            </template>
          </el-table-column>

          <el-table-column prop="myInvitationCode" label="我的邀请码" min-width="140">
            <template slot-scope="scope">
              <span class="my-invitation-code">
                {{
                  scope.row.myInvitationCode !== null
                    ? scope.row.myInvitationCode
                    : "无"
                }}
              </span>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'" size="small">
                {{ scope.row.status === 0 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="320" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button size="small" type="primary" icon="el-icon-view" @click="handleInfo(scope.row)">
                详细
              </el-button>
              <el-button size="small" type="success" icon="el-icon-data-line" @click="toPerformance(scope.row)">
                业绩
              </el-button>
              <el-button size="small" type="warning" icon="el-icon-key" @click="handleResetPassword(scope.row)">
                重置密码
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <el-pagination background layout="total, prev, pager, next, jumper" :total="filterParam.total"
        :page-size="filterParam.limit" :current-page="filterParam.page" class="modern-pagination"
        @current-change="changePage" />
    </div>

    <!-- 代理详情对话框 -->
    <el-dialog title="代理详细信息" :visible.sync="dialogVisible" width="600px" :before-close="() => {
      dialogVisible = false;
    }
      " class="proxy-dialog">
      <div class="dialog-content">
        <div class="user-info-grid">
          <div class="info-item">
            <label>用户ID</label>
            <el-input :disabled="true" :value="showData.id" />
          </div>

          <div class="info-item">
            <label>手机号</label>
            <el-input :disabled="true" :value="showData.phone" />
          </div>

          <div class="info-item">
            <label>用户名</label>
            <el-input v-model="showData.userName" placeholder="请输入用户名" />
          </div>

          <div class="info-item">
            <label>积分</label>
            <el-input v-model="showData.integral" placeholder="请输入积分" />
          </div>

          <div class="info-item">
            <label>所属邀请码</label>
            <el-input v-model="showData.invitationCode" placeholder="请输入邀请码" />
          </div>

          <div class="info-item">
            <label>我的邀请码</label>
            <el-input v-model="showData.myInvitationCode" placeholder="请输入邀请码" />
          </div>

          <div class="info-item">
            <label>注册时间</label>
            <el-input :disabled="true" :value="showData.createTime" />
          </div>

          <div class="info-item">
            <label>状态</label>
            <el-select v-model="showData.status" placeholder="请选择状态">
              <el-option :value="0" label="启用" />
              <el-option :value="1" label="禁用" />
            </el-select>
          </div>
        </div>
      </div>

      <span slot="footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="updT">修改</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { pageByPramas, updT, resetPassword } from "@/api/userList";
import { createProxy1 } from "@/api/login";
export default {
  data() {
    return {
      filterParam: {
        page: 1,
        limit: 10,
        total: 0,
        phone: "",
        adminRabk: "1",
      },
      userList: [],
      phone: "",
      adminRabk: "1",
      options: [
        {
          value: "1",
          label: "1级代理",
        },
        {
          value: "2",
          label: "2级代理",
        },
      ],
      dialogVisible: false,
      showData: {},
      phoneByProxy: "",
    };
  },
  mounted() {
    this.getUserList();
  },
  methods: {
    toPerformance(proxy) {
      console.log(proxy);
      this.$router.push({
        path: "/ProxyList/performance",
        query: {
          id: proxy.id,
          myInvitationCode: proxy.myInvitationCode,
        },
      });
    },
    getUserList() {
      const data = {
        page: this.filterParam.page,
        limit: this.filterParam.limit,
        adminRabk: this.filterParam.adminRabk,
        phone: this.filterParam.phone === "" ? null : this.filterParam.phone,
      };
      pageByPramas(data).then((res) => {
        const data = res.data;
        this.userList = data.list;
        this.filterParam.total = data.total;
      });
    },
    changePage(e) {
      this.filterParam.page = e;
      this.getUserList();
    },
    searchItem() {
      this.filterParam.phone = this.phone;
      this.filterParam.adminRabk = this.adminRabk;
      this.filterParam.page = 1;
      this.filterParam.total = 0;
      this.getUserList();
    },
    handleInfo(user) {
      this.showData = {
        ...user,
        status: user.status !== undefined ? user.status : 0
      };
      this.dialogVisible = true;
    },
    updT() {
      console.log(this.showData);
      this.$confirm("是否修改", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          updT(this.showData).then((res) => {
            if (res.data === "updSuccess") {
              this.$message.success("修改成功");
              this.dialogVisible = false;
              this.getUserList(); // 刷新列表
            } else {
              this.$message.error("修改失败");
            }
          }).catch((error) => {
            this.$message.error("修改失败");
          });
        })
        .catch(() => { });
    },
    open() {
      this.$prompt("请输入代理手机号", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern:
          /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        inputErrorMessage: "手机号格式不正确",
      })
        .then(({ value }) => {
          // 发送创建一级代理请求
          const pdata = {
            phone: value,
          };
          createProxy1(pdata).then((res) => {
            console.log(res);
            if (res.data.data == "success") {
              this.$message({
                type: "success",
                message: "添加成功",
              });
            } else {
              this.$message({
                type: "error",
                message: "该用户无法成为1级代理",
              });
            }
          });
          //
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消添加",
          });
        });
    },
    handleResetPassword(user) {
      this.$confirm(`确认要重置用户 ${user.userName}(${user.phone}) 的密码吗？`, "重置密码确认", {
        confirmButtonText: "确定重置",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true
      })
        .then(() => {
          // 调用重置密码接口
          resetPassword({ id: user.id }).then((res) => {
            if (res.data === "resetSuccess" || res.data.success) {
              this.$message.success("密码重置成功");
            } else {
              this.$message.error("密码重置失败");
            }
          }).catch((error) => {
            console.error("重置密码失败:", error);
            this.$message.error("密码重置失败，请稍后重试");
          });
        })
        .catch(() => {
          this.$message.info("已取消重置密码");
        });
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 6px;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 8px;
}

.search-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  padding: 10px;
  border: 1px solid #e5e7eb;
}

.search-header {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.search-header .search-icon {
  color: #409eff;
  font-size: 1.1rem;
  margin-right: 6px;
}

.search-header .search-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.search-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.search-input-wrapper {
  flex: 1;
  min-width: 200px;
  max-width: 300px;
}

.search-select-wrapper {
  min-width: 180px;
}

.search-button-wrapper {
  display: flex;
  gap: 6px;
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 8px;
}

.table-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%);
  padding: 8px 10px;
  border-bottom: 1px solid #e5e7eb;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
}

.table-title i {
  margin-right: 6px;
  color: #409eff;
  font-size: 1.1rem;
}

.user-id {
  font-weight: 600;
  color: #409eff;
}

.user-name-cell {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;

}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.8rem;
}

.phone-number {
  font-family: "Monaco", "Menlo", monospace;
  color: #059669;
  font-weight: 500;
}

.integral-tag {
  font-weight: 600;
}

.invitation-code,
.my-invitation-code {
  color: #6b7280;
}

/* 分页样式 */
.pagination-section {
  display: flex;
  justify-content: right;
  margin-top: 8px;
}

.dialog-content .user-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.user-info-grid .info-item label {
  display: block;
  margin-bottom: 4px;
  font-weight: 600;
  color: #374151;
  font-size: 0.8rem;
}

.cancel-btn {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  color: #6b7280;
}

.cancel-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.confirm-btn {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
  border-radius: 8px;
  margin-left: 12px;
}

.confirm-btn:hover {
  box-shadow: 0 3px 12px rgba(64, 158, 255, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 4px;
  }

  .search-content {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }

  .search-input-wrapper,
  .search-select-wrapper {
    max-width: none;
  }

  .dialog-content .user-info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}
</style>
