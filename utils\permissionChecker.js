/**
 * 权限检查工具
 * 基于adminRank字段进行严格的权限验证
 */

import {
  isLogin,
  isProxyLogin,
  isUserLogin,
  isProxyLoginStrict,
  resetInfo,
} from "./auth.js";
import store from "@/store/index.js";

// 应用启动状态跟踪
let isAppLaunching = true;
let appLaunchTimer = null;

// 设置应用启动完成（3秒后自动设置为false）
export function setAppLaunchComplete() {
  if (appLaunchTimer) {
    clearTimeout(appLaunchTimer);
  }
  appLaunchTimer = setTimeout(() => {
    isAppLaunching = false;
    console.log("[PermissionChecker] 应用启动期结束，开启严格权限检查");
  }, 3000);
}

// 手动设置应用启动状态
export function setAppLaunchingState(state) {
  isAppLaunching = state;
  console.log("[PermissionChecker] 手动设置应用启动状态:", state);
}

/**
 * 获取当前用户的adminRank
 * @returns {string} adminRank值 ("0"=普通用户, "1","2","3"=代理)
 */
export function getCurrentUserAdminRank() {
  try {
    // 先检查登录状态，如果未登录则直接返回默认值
    const userLoggedIn = isUserLogin();
    const proxyLoggedIn = isProxyLoginStrict();

    if (!userLoggedIn && !proxyLoggedIn) {
      console.log("[PermissionChecker] 用户未登录，返回默认adminRank: 0");
      return "0";
    }

    // 如果有代理端登录状态，优先从代理端store获取
    if (proxyLoggedIn) {
      const proxyUserInfo = store.getters.proxyUserInfo;
      if (proxyUserInfo && proxyUserInfo.adminRabk) {
        return proxyUserInfo.adminRabk;
      }
    }

    // 如果有用户端登录状态，从用户端store获取
    if (userLoggedIn) {
      const userInfo = store.getters.userInfo;
      if (userInfo && userInfo.adminRabk) {
        return userInfo.adminRabk;
      }
    }

    // 默认返回普通用户
    return "0";
  } catch (error) {
    console.warn("[PermissionChecker] 获取adminRank失败:", error);
    return "0";
  }
}

/**
 * 检查用户端页面访问权限
 * @param {string} pageName - 页面名称（用于日志）
 * @returns {Object} 检查结果 { hasPermission: boolean, redirectUrl?: string, message?: string }
 */
export function checkUserPagePermission(pageName = "用户端页面") {
  console.log(`[PermissionChecker] 检查${pageName}访问权限`);

  const userLoggedIn = isUserLogin();
  const proxyLoggedIn = isProxyLoginStrict();

  console.log(`[PermissionChecker] ${pageName}权限检查:`, {
    userLoggedIn,
    proxyLoggedIn,
  });

  // 如果用户未登录，允许访问用户端页面（首页等公共页面）
  if (!userLoggedIn && !proxyLoggedIn) {
    console.log(`[PermissionChecker] 用户未登录，允许访问${pageName}`);
    return { hasPermission: true };
  }

  // 如果有代理端登录状态，拒绝访问用户端页面
  if (proxyLoggedIn) {
    return {
      hasPermission: false,
      redirectUrl: "/pages/proxyIndex/index",
      message: "代理用户请使用代理端页面",
    };
  }

  // 如果只有用户端登录，还需要检查adminRank
  if (userLoggedIn) {
    const adminRank = getCurrentUserAdminRank();
    console.log(`[PermissionChecker] 已登录用户adminRank:`, adminRank);

    // 如果是代理用户但没有代理端登录状态，这是不一致的状态
    if (["1", "2", "3"].includes(adminRank)) {
      return {
        hasPermission: false,
        redirectUrl: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
        message: "代理用户请使用代理端登录",
      };
    }
  }

  // 普通用户或未登录用户可以访问
  return { hasPermission: true };
}

/**
 * 检查代理端页面访问权限
 * @param {string} pageName - 页面名称（用于日志）
 * @returns {Object} 检查结果 { hasPermission: boolean, redirectUrl?: string, message?: string }
 */
export function checkProxyPagePermission(pageName = "代理端页面") {
  console.log(`[PermissionChecker] 检查${pageName}访问权限`);
  
  const adminRank = getCurrentUserAdminRank();
  const userLoggedIn = isUserLogin();
  const proxyLoggedIn = isProxyLoginStrict();
  
  console.log(`[PermissionChecker] ${pageName}权限检查:`, {
    adminRank,
    userLoggedIn,
    proxyLoggedIn,
  });
  
  // 必须是代理用户且有代理端登录状态
  if (!["1", "2", "3"].includes(adminRank) || !proxyLoggedIn) {
    return {
      hasPermission: false,
      redirectUrl: "/pages/my/my",
      message: "普通用户无法访问代理端页面",
    };
  }
  
  // 代理用户可以访问
  return { hasPermission: true };
}

/**
 * 检查登录状态一致性
 * @param {Object} options - 选项
 * @param {boolean} options.skipDuringRestore - 是否在用户信息恢复期间跳过检查
 * @returns {Object} 检查结果 { isConsistent: boolean, action?: string, message?: string }
 */
export function checkLoginConsistency(options = {}) {
  const userLoggedIn = isUserLogin();
  const proxyLoggedIn = isProxyLoginStrict();

  console.log("[PermissionChecker] 检查登录状态一致性:", {
    userLoggedIn,
    proxyLoggedIn,
    skipDuringRestore: options.skipDuringRestore,
    isAppLaunching,
  });

  // 如果用户完全未登录，状态是一致的
  if (!userLoggedIn && !proxyLoggedIn) {
    console.log("[PermissionChecker] 用户未登录，状态一致");
    return { isConsistent: true };
  }

  // 如果在应用启动期间，跳过严格的一致性检查
  if (isAppLaunching) {
    console.log("[PermissionChecker] 应用启动期间，跳过严格一致性检查");
    return { isConsistent: true };
  }

  // 如果在用户信息恢复期间，跳过严格的一致性检查
  if (options.skipDuringRestore) {
    console.log("[PermissionChecker] 用户信息恢复期间，跳过严格一致性检查");
    return { isConsistent: true };
  }

  // 只有在用户已登录的情况下才检查adminRank一致性
  if (userLoggedIn || proxyLoggedIn) {
    const adminRank = getCurrentUserAdminRank();
    console.log("[PermissionChecker] 已登录用户adminRank:", adminRank);

    // 检查store中是否有用户信息，如果没有可能是还在恢复中
    const hasUserInfo = store.getters.userInfo && store.getters.userInfo.phone;
    const hasProxyUserInfo = store.getters.proxyUserInfo && store.getters.proxyUserInfo.phone;

    if (proxyLoggedIn && !hasProxyUserInfo) {
      console.log("[PermissionChecker] 代理端用户信息还在恢复中，跳过一致性检查");
      return { isConsistent: true };
    }

    if (userLoggedIn && !hasUserInfo) {
      console.log("[PermissionChecker] 用户端用户信息还在恢复中，跳过一致性检查");
      return { isConsistent: true };
    }

    // 普通用户但有代理端登录状态
    if (adminRank === "0" && proxyLoggedIn) {
      return {
        isConsistent: false,
        action: "clearProxy",
        message: "检测到状态不一致：普通用户但有代理端登录状态",
      };
    }

    // 代理用户但没有代理端登录状态
    if (["1", "2", "3"].includes(adminRank) && !proxyLoggedIn && userLoggedIn) {
      return {
        isConsistent: false,
        action: "relogin",
        message: "检测到状态不一致：代理用户但无代理端登录状态",
      };
    }
  }

  return { isConsistent: true };
}

/**
 * 页面权限检查的通用处理函数
 * @param {string} pageType - 页面类型 ("user" | "proxy")
 * @param {string} pageName - 页面名称
 * @param {Object} options - 选项
 * @param {boolean} options.skipDuringRestore - 是否在用户信息恢复期间跳过检查
 * @returns {boolean} 是否有权限访问
 */
export function handlePagePermissionCheck(pageType, pageName, options = {}) {
  // 先检查登录状态一致性
  const consistencyResult = checkLoginConsistency(options);
  if (!consistencyResult.isConsistent) {
    console.warn(`[PermissionChecker] ${consistencyResult.message}`);

    if (consistencyResult.action === "clearProxy") {
      // 清除不一致的代理端状态
      resetInfo();
    }

    uni.showToast({
      title: "登录状态异常，请重新登录",
      icon: "none",
      duration: 2000,
    });

    setTimeout(() => {
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin",
      });
    }, 2000);

    return false;
  }
  
  // 检查页面访问权限
  let permissionResult;
  if (pageType === "user") {
    permissionResult = checkUserPagePermission(pageName);
  } else if (pageType === "proxy") {
    permissionResult = checkProxyPagePermission(pageName);
  } else {
    console.error(`[PermissionChecker] 未知的页面类型: ${pageType}`);
    return true; // 未知类型默认允许访问
  }
  
  if (!permissionResult.hasPermission) {
    uni.showToast({
      title: permissionResult.message,
      icon: "none",
      duration: 2000,
    });
    
    setTimeout(() => {
      uni.reLaunch({
        url: permissionResult.redirectUrl,
      });
    }, 2000);
    
    return false;
  }
  
  return true;
}
