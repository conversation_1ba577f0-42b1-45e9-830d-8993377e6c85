<template>
  <view class="personal-performance-container">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <view class="nav-title">个人业绩</view>
      <view class="nav-subtitle">{{ proxyInfo.relationship === "" ? proxy.userName : proxyInfo.relationship }}</view>
    </view>

    <!-- 时间导航栏 -->
    <view class="tab-navigation">
      <view class="tab-container">
        <view class="tab-item" :class="{ 'tab-active': checkNav === 0 }" @click="clickNav(0)">
          <view class="tab-text">日报</view>
          <view v-if="checkNav === 0" class="tab-indicator"></view>
        </view>
        <view class="tab-item" :class="{ 'tab-active': checkNav === 1 }" @click="clickNav(1)">
          <view class="tab-text">周报</view>
          <view v-if="checkNav === 1" class="tab-indicator"></view>
        </view>
        <view class="tab-item" :class="{ 'tab-active': checkNav === 2 }" @click="clickNav(2)">
          <view class="tab-text">月报</view>
          <view v-if="checkNav === 2" class="tab-indicator"></view>
        </view>
        <view class="tab-item" :class="{ 'tab-active': checkNav === 3 }" @click="clickNav(3)">
          <view class="tab-text">自定义</view>
          <view v-if="checkNav === 3" class="tab-indicator"></view>
        </view>
      </view>
    </view>

    <!-- 时间显示 -->
    <view class="date-display" @click="showDatePicker">{{ dateString }}</view>

    <!-- 日期选择器弹窗 -->
    <view v-if="showDatePickerModal" class="date-picker-modal">
      <view class="modal-mask" @click="closeDatePicker"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">选择日期范围</text>
          <text class="close-btn" @click="closeDatePicker">×</text>
        </view>

        <view class="date-picker-container">
          <view class="date-picker-item">
            <text class="date-label">开始日期：</text>
            <picker mode="date" :value="tempStartDate" @change="onStartDateChange" :end="tempEndDate">
              <view class="date-picker-value">{{ tempStartDate || '请选择' }}</view>
            </picker>
          </view>

          <view class="date-picker-item">
            <text class="date-label">结束日期：</text>
            <picker mode="date" :value="tempEndDate" @change="onEndDateChange" :start="tempStartDate">
              <view class="date-picker-value">{{ tempEndDate || '请选择' }}</view>
            </picker>
          </view>
        </view>

        <view class="modal-footer">
          <button class="cancel-btn" @click="closeDatePicker">取消</button>
          <button class="confirm-btn" @click="confirmDateSelection">确定</button>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <!-- 完成订单卡片 -->
      <view class="order-card" @click="toOrderList('0')">
        <view class="order-header">
          <view class="order-title">
            <view class="title-text">已完成订单</view>
          </view>
          <view class="arrow-icon">›</view>
        </view>

        <view class="order-info">
          <view class="info-row">
            <view class="info-label">订单数</view>
            <view class="info-value">{{
              proxyInfo.completeCount === undefined
                ? "0"
                : proxyInfo.completeCount
            }}</view>
          </view>
          <view class="info-row">
            <view class="info-label">金额数</view>
            <view class="info-value info-price">{{
              proxyInfo.allMoney === null ? "0.00" : proxyInfo.allMoney.toFixed(2)
            }}￥</view>
          </view>
        </view>

        <!-- 商品列表 -->
        <view v-if="proxyInfo.goods && proxyInfo.goods.length > 0" class="goods-section">
          <view class="goods-title">商品明细</view>
          <view class="goods-list">
            <view class="goods-item" v-for="(data, index) in proxyInfo.goods" :key="index">
              <view class="goods-name">{{ data.name }}</view>
              <view class="goods-count">{{ data.count }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 退货订单卡片 -->
      <view class="order-card" @click="toOrderList('1')">
        <view class="order-header">
          <view class="order-title">
            <view class="title-text">退货订单</view>
          </view>
          <view class="arrow-icon">›</view>
        </view>

        <view class="order-info">
          <view class="info-row">
            <view class="info-label">退货订单</view>
            <view class="info-value">{{ proxyInfo.FailCount || 0 }}</view>
          </view>
          <view class="info-row">
            <view class="info-label">退款金额</view>
            <view class="info-value info-highlight">{{
              proxyInfo.FailMoney === null
                ? "0.00"
                : proxyInfo.FailMoney.toFixed(2)
            }}￥</view>
          </view>
        </view>

        <!-- 退款明细 -->
        <view v-if="proxyInfo.refundOrders && proxyInfo.refundOrders.length > 0" class="refund-section">
          <view class="refund-title">退款明细</view>
          <view class="refund-list">
            <view class="refund-item" v-for="(data, index) in proxyInfo.refundOrders" :key="index">
              <view class="refund-info">
                <view class="refund-phone">手机号：{{ data.user.phnumber }}</view>
                <view class="refund-car">车牌号：{{ data.user.carId }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDateByDay } from "@/utils/dateUtil.js";
import { getInformationsByData } from "@/subpackages/proxy/api/proxyPerformance.js";
import { getUserId } from "@/utils/auth.js";

const getTime = function (str = "", flog) {
  if (flog === false) {
    return str.replaceAll("/", "-") + " 00:00:00";
  }
  return str.replaceAll("/", "-") + " 23:59:59";
};

const toLocaleDateString = function (data = new Date()) {
  let res = "";
  res += data.getFullYear() + "/";
  res += data.getMonth() + 1 + "/";
  res += data.getDate();
  return res;
};

export default {
  data() {
    return {
      checkNav: 0,
      range: ["", ""],
      proxy: {
        userName: "",
      },
      proxyInfo: {
        count: 0,
        FailMoney: 0,
        FailCount: 0,
        allMoney: 0,
        goods: [],
        Fail: [],
        relationship: "",
        finishOrders: [],
        refundOrders: [],
      },
      // 日期选择器相关状态
      showDatePickerModal: false,
      tempStartDate: '',
      tempEndDate: '',
    };
  },
  methods: {
    clickNav(index) {
      this.checkNav = index;
      if (index !== 3) {
        let start;
        let end = toLocaleDateString(getDateByDay(0));
        if (index === 0) {
          start = end;
        } else if (index === 1) {
          start = toLocaleDateString(getDateByDay(6));
        } else if (index === 2) {
          start = toLocaleDateString(getDateByDay(30));
        }
        this.range = [start, end];
        this.getData();
      } else {
        // 选择自定义时显示日期选择器
        this.showDatePicker();
      }
    },
    getData() {
      console.log(this.proxy);
      uni.showLoading({
        title: "加载中",
      });
      const data = {
        userid: this.proxy.id,
        startTime: getTime(this.range[0], false),
        endTime: getTime(this.range[1], true),
      };
      getInformationsByData(data)
        .then((res) => {
          if (res.data.code !== 200) {
            return;
          }
          this.proxyInfo = res.data.data;
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    changeDate() {
      this.getData();
    },
    toOrderList(type) {
      if (this.proxy.id !== getUserId()) {
        return;
      }
      const data = {
        range: this.range.map((o) => {
          return o.replaceAll("/", "-");
        }),
        type,
      };
      uni.navigateTo({
        url: "/subpackages/proxy/pages/proxyOrderList/orderList?data=" + JSON.stringify(data),
      });
    },
    // 显示日期选择器
    showDatePicker() {
      // 初始化临时日期为当前范围的日期
      if (this.range && this.range.length === 2) {
        this.tempStartDate = this.range[0].replaceAll('/', '-');
        this.tempEndDate = this.range[1].replaceAll('/', '-');
      } else {
        // 默认设置为今天
        const today = this.formatDateToString(new Date());
        this.tempStartDate = today;
        this.tempEndDate = today;
      }
      this.showDatePickerModal = true;
    },

    // 关闭日期选择器
    closeDatePicker() {
      this.showDatePickerModal = false;
    },

    // 开始日期改变
    onStartDateChange(e) {
      this.tempStartDate = e.detail.value;
    },

    // 结束日期改变
    onEndDateChange(e) {
      this.tempEndDate = e.detail.value;
    },

    // 确认日期选择
    confirmDateSelection() {
      if (!this.tempStartDate || !this.tempEndDate) {
        uni.showToast({
          title: '请选择完整的日期范围',
          icon: 'none'
        });
        return;
      }

      // 验证日期范围
      const startDate = new Date(this.tempStartDate);
      const endDate = new Date(this.tempEndDate);

      if (startDate > endDate) {
        uni.showToast({
          title: '开始日期不能大于结束日期',
          icon: 'none'
        });
        return;
      }

      // 更新日期范围
      this.range = [
        this.tempStartDate.replaceAll('-', '/'),
        this.tempEndDate.replaceAll('-', '/')
      ];

      // 关闭弹窗
      this.closeDatePicker();

      // 刷新数据
      this.getData();
    },

    // 格式化日期为字符串
    formatDateToString(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
  },
  onLoad(query) {
    const data = JSON.parse(query.data);
    this.proxy = data.proxy;
    const range = data.range;
    this.range = range;
    this.checkNav = data.checkNav;
    this.getData();
  },
  computed: {
    dateString() {
      let start = this.range[0];
      let end = this.range[1];
      console.log(start, end);
      start = start.replaceAll("-", "/");
      end = end.replaceAll("-", "/");
      const list1 = start.split("/");
      const list2 = end.split("/");
      let res = "";
      if (list1.length === 0) {
        return res;
      }
      res = list1[0] + "年" + list1[1] + "月" + list1[2] + "日";
      if (start === end) {
        return res;
      }
      res += " 至 " + list2[0] + "年" + list2[1] + "月" + list2[2] + "日";
      return res;
    },
  },
};
</script>

<style lang="scss">
/* 页面容器 */
.personal-performance-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
}

/* 顶部导航栏 */
.nav-header {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;

  .nav-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  .nav-subtitle {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
  }
}

/* 标签导航栏 */
.tab-navigation {
  background: white;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .tab-container {
    display: flex;
    justify-content: space-around;

    .tab-item {
      position: relative;
      padding: 16px 8px;
      flex: 1;
      text-align: center;
      transition: all 0.3s ease;

      .tab-text {
        font-size: 14px;
        color: #666;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      .tab-indicator {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 3px;
        background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
        border-radius: 2px;
        animation: slideIn 0.3s ease;
      }

      &.tab-active {
        .tab-text {
          color: #00d4aa;
          font-weight: 600;
        }
      }
    }
  }
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }

  to {
    width: 30px;
    opacity: 1;
  }
}

/* 时间显示 */
.date-display {
  text-align: center;
  padding: 15px 20px;
  font-size: 14px;
  color: #666;
  background: white;
  margin: 20px 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 212, 170, 0.08);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.date-display:hover {
  border-color: #00d4aa;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.15);
}

.date-display:active {
  transform: scale(0.98);
}

/* 内容区域 */
.content-wrapper {
  padding: 0 20px 20px;
}

/* 日期选择器弹窗样式 */
.date-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background: white;
  border-radius: 16px;
  margin: 0 40px;
  max-width: 400px;
  width: 100%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  font-size: 24px;
  color: #999;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.date-picker-container {
  padding: 20px;
}

.date-picker-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.date-picker-item:last-child {
  margin-bottom: 0;
}

.date-label {
  width: 80px;
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}

.date-picker-value {
  flex: 1;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  border: 1px solid #e9ecef;
}

.modal-footer {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 16px;
  font-size: 16px;
  border: none;
  background: none;
  cursor: pointer;
}

.cancel-btn {
  color: #666;
  border-right: 1px solid #f0f0f0;
}

.confirm-btn {
  color: #00d4aa;
  font-weight: 600;
}

/* 订单卡片 */
.order-card {
  background: white;
  border-radius: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }

  /* 订单头部 */
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 16px;
    border-bottom: 1px solid #f5f5f5;

    .order-title {
      display: flex;
      align-items: center;
      flex: 1;

      .title-text {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        line-height: 1.4;
      }
    }

    .arrow-icon {
      font-size: 20px;
      color: #00d4aa;
      font-weight: 600;
    }
  }

  /* 订单信息 */
  .order-info {
    padding: 16px 20px;

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
        min-width: 80px;
      }

      .info-value {
        font-size: 14px;
        color: #333;
        flex: 1;
        text-align: right;
        word-break: break-all;
      }

      .info-price {
        font-size: 16px;
        color: #00d4aa;
        font-weight: 600;
      }

      .info-highlight {
        font-size: 16px;
        color: #ff6b35;
        font-weight: 600;
      }
    }
  }

  /* 商品部分 */
  .goods-section {
    border-top: 1px solid #f5f5f5;
    padding: 16px 20px 20px;

    .goods-title {
      font-size: 14px;
      color: #666;
      font-weight: 500;
      margin-bottom: 12px;
    }

    .goods-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .goods-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(0, 212, 170, 0.1);
        border: 1px solid rgba(0, 212, 170, 0.2);
        border-radius: 12px;
        padding: 8px 12px;
        min-width: 120px;

        .goods-name {
          font-size: 13px;
          color: #00d4aa;
          font-weight: 500;
        }

        .goods-count {
          font-size: 13px;
          color: #00d4aa;
          font-weight: 600;
          margin-left: 8px;
        }
      }
    }
  }

  /* 退款部分 */
  .refund-section {
    border-top: 1px solid #f5f5f5;
    padding: 16px 20px 20px;

    .refund-title {
      font-size: 14px;
      color: #666;
      font-weight: 500;
      margin-bottom: 12px;
    }

    .refund-list {
      .refund-item {
        background: rgba(255, 107, 53, 0.1);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 12px;
        padding: 12px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .refund-info {

          .refund-phone,
          .refund-car {
            font-size: 13px;
            color: #ff6b35;
            font-weight: 500;
            margin-bottom: 4px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}
</style>
