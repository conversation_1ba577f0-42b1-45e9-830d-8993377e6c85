<template>
  <view class="etc-container">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <view class="nav-title">ETC办理</view>
      <view class="nav-subtitle">选择适合您的ETC卡类型</view>
    </view>

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <view v-for="(item, index) in boxData" :key="index" class="etc-section">
        <!-- 区域标题 -->
        <view class="section-title">
          <view class="title-indicator"></view>
          <text class="title-text">{{ item.title }}</text>
          <view class="title-badge" v-if="item.type === '1'">
            <uni-icons type="forward" size="12" color="#00d4aa"></uni-icons>
            <text>可办理</text>
          </view>
        </view>

        <!-- 二维码图片 -->
        <view v-if="item.type === '0'" class="qr-container">
          <image :src="item.img" mode="widthFix" class="qr-image" show-menu-by-longpress="true"
            @click="previewImage(item.img)"></image>
          <view class="qr-tip">
            <uni-icons type="info" size="14" color="#666"></uni-icons>
            <text>长按保存二维码或点击查看大图</text>
          </view>
        </view>

        <!-- 可办理卡片 -->
        <view v-else class="etc-card" @click="toUrl(item)">
          <view class="card-content">
            <view class="card-image-container">
              <image :src="item.img" mode="aspectFit" class="card-image"></image>
            </view>
            <view class="card-info">
              <view class="card-name">{{ item.title }}</view>
              <view class="card-desc">一卡畅通全国高速</view>
              <view class="card-features">
                <view class="feature-item">
                  <uni-icons type="checkmarkempty" size="12" color="#00d4aa"></uni-icons>
                  <text>免费办理</text>
                </view>
                <view class="feature-item">
                  <uni-icons type="checkmarkempty" size="12" color="#00d4aa"></uni-icons>
                  <text>全国通用</text>
                </view>
              </view>
            </view>
            <view class="card-action">
              <uni-icons type="arrowright" size="16" color="#00d4aa"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import config from "@/utils/config.js";

export default {
  data() {
    return {
      boxData: [
        // type： 0二维码图片 1跳转链接
        {
          title: "交通9901",
          img: `${config.BASE_URL}/static/交通9901.png`,
          type: "0",
        },
        {
          title: "冀通卡",
          img: `${config.BASE_URL}/static/冀通卡.jpg`,
          type: "0",
        },
        {
          title: "ETC办理",
          img: `${config.BASE_URL}/static/ETC办理.png`,
          type: "1",
        },
        {
          title: "蒙通畅行卡",
          img: `${config.BASE_URL}/static/蒙通畅行卡.png`,
          type: "0",
        },
        {
          title: "速通卡",
          img: `${config.BASE_URL}/static/速通卡.jpg`,
          type: "0",
        },
      ],
      carId: "",
    };
  },
  methods: {
    // 预览图片
    previewImage(imageUrl) {
      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl,
        success: () => {
          console.log("预览图片成功");
        },
        fail: (err) => {
          console.error("预览图片失败:", err);
          uni.showToast({
            title: "预览失败",
            icon: "none",
          });
        },
      });
    },

    // 跳转到办理页面
    toUrl(item) {
      if (item.title === "世纪恒通卡") {
        let toPath =
          "pages/empty_hair/new_module/select_etc_handle_type/select_etc_handle_type?isNewTrucks=0&shopId=1303036541465796608&vehPlates=" +
          this.carId +
          '&extend={"merchantCode":"PPVMZIYXEXGDRCZOG"}';

        uni.showLoading({
          title: "正在跳转...",
        });

        uni.navigateToMiniProgram({
          appId: "wxddb3eb32425e4a96",
          path: toPath,
          extraData: {
            type: "out",
          },
          envVersion: "release",
          success: (res) => {
            uni.hideLoading();
            uni.showToast({
              title: "跳转成功",
              icon: "success",
            });
          },
          fail: (err) => {
            uni.hideLoading();
            console.error("跳转失败:", err);
            uni.showToast({
              title: "跳转失败，请重试",
              icon: "none",
            });
          },
        });
      }
    },
  },
  onLoad: function (option) {
    this.carId = option.carId;
    console.log(this.carId);
  },
};
</script>

<style lang="scss" scoped>
/* 页面容器 */
.etc-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
}

/* 顶部导航栏 */
.nav-header {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;

  .nav-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  .nav-subtitle {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
  }
}

/* 内容区域 */
.content-wrapper {
  padding: 20px;
}

.etc-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 40px;
  }
}

/* 区域标题 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .title-indicator {
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
    border-radius: 2px;
    margin-right: 12px;
  }

  .title-text {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .title-badge {
    display: flex;
    align-items: center;
    background: rgba(0, 212, 170, 0.1);
    color: #00d4aa;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;

    text {
      margin-left: 4px;
    }
  }
}

/* 二维码容器 */
.qr-container {
  background: white;
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);

  .qr-image {
    width: 100%;
    max-width: 280px;
    border-radius: 12px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }
  }

  .qr-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;
    color: #666;
    font-size: 13px;

    text {
      margin-left: 6px;
    }
  }
}

/* ETC卡片 */
.etc-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4px 20px rgba(0, 212, 170, 0.15);
  }

  .card-content {
    display: flex;
    align-items: center;
    padding: 20px;

    .card-image-container {
      width: 80px;
      height: 80px;
      margin-right: 16px;
      border-radius: 12px;
      overflow: hidden;
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;

      .card-image {
        width: 100%;
        height: 100%;
      }
    }

    .card-info {
      flex: 1;

      .card-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 6px;
      }

      .card-desc {
        font-size: 13px;
        color: #999;
        margin-bottom: 12px;
      }

      .card-features {
        display: flex;
        gap: 16px;

        .feature-item {
          display: flex;
          align-items: center;

          text {
            margin-left: 4px;
            font-size: 12px;
            color: #666;
          }
        }
      }
    }

    .card-action {
      padding: 8px;
      border-radius: 8px;
      background: rgba(0, 212, 170, 0.1);
      transition: all 0.3s ease;
    }
  }
}
</style>
