# just a flag
ENV = 'development'
NODE_ENV = 'development'

# base api - 开发环境使用代理路径
# 实际后台API地址（用于代理配置）
VUE_APP_PROXY_TARGET = "https://drtjza50.beesnat.com"
# 开发环境API基础路径（通过代理访问）
VUE_APP_BASE_API = "/api"

# 备用后台地址（注释保留）
# VUE_APP_PROXY_TARGET = 'https://zk.yaoxuankeji.club:8091'
# VUE_APP_PROXY_TARGET = 'http://*************:8011'
# VUE_APP_PROXY_TARGET = 'http://localhost:8012'
# VUE_APP_PROXY_TARGET = 'http://localhost:8019'
# VUE_APP_PROXY_TARGET = 'https://elcxt.online:8019'

# 开发环境优化配置
VUE_APP_PORT = 9528
VUE_APP_MOCK = true
VUE_APP_ESLINT = false
VUE_APP_CACHE = true
VUE_APP_SOURCEMAP = false




