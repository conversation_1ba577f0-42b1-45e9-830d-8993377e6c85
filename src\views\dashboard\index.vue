﻿<template>
  <div class="dashboard-container">
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">欢迎使用 乐行车畅通管理系统</h1>
        <p class="welcome-subtitle">管理您的业务数据，掌控全局</p>
      </div>
      <div class="welcome-decoration">
        <div class="decoration-circle circle-1" />
        <div class="decoration-circle circle-2" />
        <div class="decoration-circle circle-3" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "Dashboard",
  computed: {
    ...mapGetters(["name"]),
  },
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  margin: 30px;
  padding: 0;
}

.welcome-section {
  position: relative;
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 10px;
  overflow: hidden;
  color: white;
  box-shadow: 0 10px 30px rgba(64, 158, 255, 0.3);

  .welcome-content {
    position: relative;
    z-index: 2;
  }

  .welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 15px 0;
    background: linear-gradient(45deg, #ffffff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .welcome-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 300;
  }

  .welcome-decoration {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
  }

  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);

    &.circle-1 {
      width: 120px;
      height: 120px;
      top: -60px;
      right: -60px;
      animation: float 6s ease-in-out infinite;
    }

    &.circle-2 {
      width: 80px;
      height: 80px;
      top: 50px;
      right: 80px;
      animation: float 4s ease-in-out infinite reverse;
    }

    &.circle-3 {
      width: 40px;
      height: 40px;
      bottom: 20px;
      right: 200px;
      animation: float 8s ease-in-out infinite;
    }
  }
}

.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 8px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 10px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);

    &::before {
      transform: scaleX(1);
    }

    .stat-icon {
      transform: scale(1.1) rotate(5deg);
    }
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 24px;
    color: white;
    transition: all 0.3s ease;

    &.users-icon {
      background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
    }

    &.orders-icon {
      background: linear-gradient(135deg, #f56c6c 0%, #f56c6c 100%);
    }

    &.data-icon {
      background: linear-gradient(135deg, #67c23a 0%, #67c23a 100%);
    }

    &.settings-icon {
      background: linear-gradient(135deg, #67c23a 0%, #67c23a 100%);
    }
  }

  .stat-content {
    flex: 1;

    h3 {
      margin: 0 0 8px 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: #2d3748;
    }

    p {
      margin: 0;
      color: #718096;
      font-size: 0.9rem;
    }
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-20px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    margin: 15px;
  }

  .welcome-section {
    padding: 25px;
  }

  .welcome-title {
    font-size: 2rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .quick-stats {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .stat-card {
    padding: 18px;
  }
}
</style>
